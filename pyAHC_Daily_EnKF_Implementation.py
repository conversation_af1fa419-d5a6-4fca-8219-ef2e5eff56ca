#!/usr/bin/env python3
"""
pyAHC每日数据同化实现
通过动态调整end_date实现每日同化
"""

import numpy as np
import pandas as pd
from datetime import datetime, timedelta, date
from typing import Dict, List, Any, Tuple
from pathlib import Path
import copy

from pyahc.model.model import Model
from KFs import EnsembleKalmanFilter as EnKF

class PyAHC_DailyEnKF:
    """pyAHC每日数据同化器"""
    
    def __init__(self, base_config: Dict, ensemble_size: int = 50):
        """
        初始化每日数据同化系统
        
        参数:
        - base_config: 基础模型配置
        - ensemble_size: 集合大小
        """
        self.base_config = base_config
        self.ensemble_size = ensemble_size
        
        # 提取时间信息
        self.start_date = base_config['generalsettings']['tstart']
        self.original_end_date = base_config['generalsettings']['tend']
        
        # 状态变量定义
        self.state_variables = [
            'soil_moisture_0_20cm',
            'soil_moisture_20_40cm', 
            'soil_moisture_40_80cm',
            'groundwater_level',
            'evapotranspiration'
        ]
        
        # 初始化集合参数
        self.ensemble_parameters = self._generate_ensemble_parameters()
        
        # 存储每日状态用于重启
        self.daily_states = {}
        
        # 结果存储
        self.results = {
            'daily_states': [],
            'daily_covariances': [],
            'observations_used': [],
            'innovations': []
        }
        
        print(f"初始化每日EnKF: 集合大小={ensemble_size}, 模拟期间={self.start_date} 到 {self.original_end_date}")
    
    def run_daily_assimilation(self, observations: Dict[str, np.ndarray]) -> Dict[str, Any]:
        """
        运行每日数据同化
        
        参数:
        - observations: 观测数据 {date_string: observation_vector}
        
        返回:
        - 同化结果
        """
        print("开始每日数据同化...")
        
        # 初始化EnKF
        self._initialize_enkf()
        
        # 计算总天数
        total_days = (self.original_end_date - self.start_date).days + 1
        
        for day in range(1, total_days + 1):
            current_date = self.start_date + timedelta(days=day-1)
            current_end_date = self.start_date + timedelta(days=day)
            date_str = current_date.strftime('%Y-%m-%d')
            
            print(f"第 {day} 天: {date_str}")
            
            # 1. 运行集合到当前日期
            ensemble_states = self._run_ensemble_to_date(current_end_date, day)
            
            # 2. 检查是否有观测数据
            if date_str in observations:
                obs_vector = observations[date_str]
                
                # 3. EnKF更新
                self._enkf_update_step(ensemble_states, obs_vector, day)
                
                # 4. 计算创新
                innovation = obs_vector - self._compute_observation_from_states(ensemble_states)
                self.results['innovations'].append({
                    'day': day,
                    'date': date_str,
                    'innovation': innovation
                })
                
                print(f"  同化观测: {obs_vector}")
                
                # 5. 更新集合状态用于下一天
                self._update_ensemble_for_next_day(current_end_date, day)
            
            # 6. 记录结果
            self._record_daily_results(ensemble_states, day, date_str)
            
            if day % 10 == 0:
                print(f"  完成 {day}/{total_days} 天")
        
        print("每日数据同化完成!")
        return self.results
    
    def _run_ensemble_to_date(self, end_date: date, day: int) -> List[np.ndarray]:
        """运行集合到指定日期"""
        ensemble_states = []
        
        for member_id in range(self.ensemble_size):
            # 创建成员配置
            member_config = self._create_member_config(member_id, end_date, day)
            
            try:
                # 运行模型
                model = Model(**member_config)
                result = model.run(silence_warnings=True)
                
                # 提取状态
                member_state = self._extract_member_state(result)
                ensemble_states.append(member_state)
                
            except Exception as e:
                print(f"  警告: 集合成员 {member_id} 运行失败: {e}")
                # 使用默认状态
                default_state = np.array([0.3, 0.25, 0.2, -100.0, 0.0])
                ensemble_states.append(default_state)
        
        return ensemble_states
    
    def _create_member_config(self, member_id: int, end_date: date, day: int) -> Dict:
        """创建集合成员配置"""
        member_config = copy.deepcopy(self.base_config)
        
        # 设置结束日期
        member_config['generalsettings']['tend'] = end_date
        
        # 应用参数扰动
        member_params = self.ensemble_parameters[member_id]
        member_config = self._apply_parameter_perturbations(member_config, member_params)
        
        # 如果不是第一天，应用状态更新
        if day > 1 and member_id in self.daily_states:
            member_config = self._apply_state_updates(member_config, member_id, day)
        
        return member_config
    
    def _extract_member_state(self, result) -> np.ndarray:
        """从模型结果中提取状态变量"""
        try:
            if hasattr(result, 'output') and 'csv' in result.output:
                df = result.output['csv']
                if not df.empty:
                    last_row = df.iloc[-1]
                    
                    states = []
                    
                    # 提取土壤水分（不同深度）
                    soil_moisture_cols = ['THETA_1', 'THETA_2', 'THETA_3', 'SM_1', 'SM_2', 'SM_3']
                    for i in range(3):  # 3层土壤
                        found = False
                        for col_pattern in soil_moisture_cols:
                            col_name = col_pattern if col_pattern in last_row.index else f"{col_pattern}_{i+1}"
                            if col_name in last_row.index:
                                states.append(float(last_row[col_name]))
                                found = True
                                break
                        if not found:
                            states.append(0.3 - i * 0.05)  # 默认递减值
                    
                    # 提取地下水位
                    gwl_cols = ['GWL', 'GROUNDWATER_LEVEL', 'WTD']
                    gwl_found = False
                    for col in gwl_cols:
                        if col in last_row.index:
                            states.append(float(last_row[col]))
                            gwl_found = True
                            break
                    if not gwl_found:
                        states.append(-100.0)
                    
                    # 提取蒸散发
                    et_cols = ['ET', 'EVAPOTRANSPIRATION', 'ETa']
                    et_found = False
                    for col in et_cols:
                        if col in last_row.index:
                            states.append(float(last_row[col]))
                            et_found = True
                            break
                    if not et_found:
                        states.append(0.0)
                    
                    return np.array(states)
            
            # 如果提取失败，返回默认状态
            return np.array([0.3, 0.25, 0.2, -100.0, 0.0])
            
        except Exception as e:
            print(f"状态提取失败: {e}")
            return np.array([0.3, 0.25, 0.2, -100.0, 0.0])
    
    def _initialize_enkf(self):
        """初始化EnKF"""
        dim_x = len(self.state_variables)
        dim_z = 2  # 假设观测土壤水分和地下水位
        
        x0 = np.array([0.3, 0.25, 0.2, -100.0, 0.0])
        P0 = np.diag([0.01, 0.01, 0.01, 100.0, 1.0])
        
        self.enkf = EnKF(
            x=x0,
            P=P0,
            dim_z=dim_z,
            N=self.ensemble_size,
            hx=self._observation_operator,
            fx=None  # 我们不使用fx，而是直接运行模型
        )
        
        # 设置观测误差
        self.enkf.R = np.diag([0.05**2, 10.0**2])  # 土壤水分和地下水位的观测误差
    
    def _observation_operator(self, state: np.ndarray) -> np.ndarray:
        """观测算子：从状态向量中提取观测量"""
        # 返回表层土壤水分和地下水位
        return np.array([state[0], state[3]])
    
    def _enkf_update_step(self, ensemble_states: List[np.ndarray], 
                         observations: np.ndarray, day: int):
        """执行EnKF更新步骤"""
        # 将集合状态转换为EnKF格式
        self.enkf.sigmas = np.array(ensemble_states)
        self.enkf.x = np.mean(self.enkf.sigmas, axis=0)
        self.enkf.P = np.cov(self.enkf.sigmas.T)
        
        # 执行更新
        self.enkf.update(observations)
        
        # 更新集合状态
        updated_ensemble = self.enkf.sigmas
        
        # 存储更新后的状态
        for member_id, updated_state in enumerate(updated_ensemble):
            if member_id not in self.daily_states:
                self.daily_states[member_id] = {}
            self.daily_states[member_id][day] = updated_state.copy()
    
    def _compute_observation_from_states(self, ensemble_states: List[np.ndarray]) -> np.ndarray:
        """从集合状态计算观测预测值"""
        ensemble_mean = np.mean(ensemble_states, axis=0)
        return self._observation_operator(ensemble_mean)
    
    def _update_ensemble_for_next_day(self, current_date: date, day: int):
        """为下一天更新集合初始条件"""
        # 这里需要将EnKF更新后的状态转换为pyAHC的初始条件
        # 具体实现取决于pyAHC如何设置初始状态
        pass
    
    def _apply_parameter_perturbations(self, config: Dict, parameters: List[float]) -> Dict:
        """应用参数扰动"""
        # 这里应用集合参数到配置
        # 具体实现取决于参数与配置的映射关系
        return config
    
    def _apply_state_updates(self, config: Dict, member_id: int, day: int) -> Dict:
        """应用状态更新到配置"""
        if member_id in self.daily_states and (day-1) in self.daily_states[member_id]:
            updated_state = self.daily_states[member_id][day-1]
            
            # 将更新后的状态应用到配置
            # 例如：更新土壤初始含水量
            if hasattr(config.get('soilmoisture'), 'thetai'):
                # 假设thetai是一个列表，更新前几层
                config['soilmoisture']['thetai'][:3] = updated_state[:3].tolist()
            
            # 更新地下水位等其他状态...
        
        return config
    
    def _generate_ensemble_parameters(self) -> List[List[float]]:
        """生成集合参数"""
        # 定义参数均值和标准差
        param_means = [10.0, 0.4, 0.25, 1.0, 0.5]  # 示例参数
        param_stds = [3.0, 0.04, 0.025, 0.1, 0.05]
        
        ensemble_params = []
        for i in range(self.ensemble_size):
            member_params = []
            for mean, std in zip(param_means, param_stds):
                param_value = np.random.normal(mean, std)
                member_params.append(param_value)
            ensemble_params.append(member_params)
        
        return ensemble_params
    
    def _record_daily_results(self, ensemble_states: List[np.ndarray], 
                            day: int, date_str: str):
        """记录每日结果"""
        ensemble_mean = np.mean(ensemble_states, axis=0)
        ensemble_cov = np.cov(np.array(ensemble_states).T)
        
        self.results['daily_states'].append({
            'day': day,
            'date': date_str,
            'mean_state': ensemble_mean,
            'ensemble_states': ensemble_states.copy()
        })
        
        self.results['daily_covariances'].append({
            'day': day,
            'date': date_str,
            'covariance': ensemble_cov
        })
    
    def save_results(self, output_path: str):
        """保存结果"""
        output_dir = Path(output_path)
        output_dir.mkdir(parents=True, exist_ok=True)
        
        # 保存每日状态
        daily_data = []
        for item in self.results['daily_states']:
            daily_data.append({
                'day': item['day'],
                'date': item['date'],
                **{f'state_{i}': item['mean_state'][i] for i in range(len(item['mean_state']))}
            })
        
        df = pd.DataFrame(daily_data)
        df.to_csv(output_dir / 'daily_states.csv', index=False)
        
        print(f"结果已保存到: {output_path}")

# 使用示例
def create_synthetic_observations():
    """创建合成观测数据"""
    observations = {}
    start_date = date(2013, 5, 2)
    
    for day in range(0, 146, 5):  # 每5天一个观测
        current_date = start_date + timedelta(days=day)
        date_str = current_date.strftime('%Y-%m-%d')
        
        # 合成观测：土壤水分和地下水位
        soil_moisture = 0.25 + 0.05 * np.sin(day * 0.1) + np.random.normal(0, 0.02)
        groundwater_level = -80 + 10 * np.sin(day * 0.05) + np.random.normal(0, 5)
        
        observations[date_str] = np.array([soil_moisture, groundwater_level])
    
    return observations

if __name__ == "__main__":
    # 基础配置（需要根据实际情况调整）
    base_config = {
        'generalsettings': {
            'tstart': date(2013, 5, 2),
            'tend': date(2013, 9, 25)
        },
        # 其他配置...
    }
    
    # 创建每日同化系统
    daily_enkf = PyAHC_DailyEnKF(base_config, ensemble_size=30)
    
    # 创建观测数据
    observations = create_synthetic_observations()
    
    # 运行每日同化
    results = daily_enkf.run_daily_assimilation(observations)
    
    # 保存结果
    daily_enkf.save_results('results/daily_enkf')
    
    print("每日数据同化完成!")
